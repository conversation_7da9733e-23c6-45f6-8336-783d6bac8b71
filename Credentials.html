<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Development Tools & Credentials Documentation</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-card: #ffffff;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius: 8px;
            --radius-lg: 12px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .nav-tab {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-tab:hover,
        .nav-tab.active {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .content-wrapper {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .section {
            display: none;
            padding: 2rem;
        }

        .section.active {
            display: block;
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card h3 {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .credential-item {
            background: var(--bg-secondary);
            border-radius: var(--radius);
            padding: 1rem;
            margin: 0.5rem 0;
            position: relative;
        }

        .credential-label {
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.25rem;
        }

        .credential-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.95rem;
            color: var(--text-primary);
            background: white;
            padding: 0.75rem;
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
            word-break: break-all;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius);
            padding: 0.5rem;
            cursor: pointer;
            font-size: 0.75rem;
            transition: all 0.2s ease;
            opacity: 0.7;
        }

        .copy-btn:hover {
            opacity: 1;
            transform: scale(1.05);
        }

        .copy-btn.copied {
            background: var(--success-color);
        }

        .url-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: 1px solid var(--primary-color);
            border-radius: var(--radius);
            transition: all 0.3s ease;
            margin: 0.5rem 0;
        }

        .url-link:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .instructions {
            background: #f0f9ff;
            border-left: 4px solid var(--primary-color);
            padding: 1rem 1.5rem;
            margin: 1rem 0;
            border-radius: 0 var(--radius) var(--radius) 0;
        }

        .instructions h4 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .warning {
            background: #fef3c7;
            border-left: 4px solid var(--warning-color);
            padding: 1rem 1.5rem;
            margin: 1rem 0;
            border-radius: 0 var(--radius) var(--radius) 0;
        }

        .warning h4 {
            color: #92400e;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .step-list {
            list-style: none;
            counter-reset: step-counter;
        }

        .step-list li {
            counter-increment: step-counter;
            margin: 1rem 0;
            padding-left: 3rem;
            position: relative;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: var(--primary-color);
            color: white;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            background: var(--success-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .audio-player {
            background: var(--bg-secondary);
            border-radius: var(--radius);
            padding: 1rem;
            margin: 1rem 0;
        }

        .audio-player audio {
            width: 100%;
            outline: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-tabs {
                flex-direction: column;
                align-items: center;
            }

            .nav-tab {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }

            .section {
                padding: 1.5rem;
            }

            .credential-value {
                font-size: 0.8rem;
                padding: 0.5rem;
            }

            .copy-btn {
                position: static;
                margin-top: 0.5rem;
                width: 100%;
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section.active {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>

<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-key"></i> Development Tools & Credentials</h1>
            <p>Professional documentation for development tools, accounts, and workflows</p>
        </header>

        <nav class="nav-tabs">
            <div class="nav-tab active" onclick="showSection('testing')">
                <i class="fas fa-mobile-alt"></i> Testing Tools
            </div>
            <div class="nav-tab" onclick="showSection('content')">
                <i class="fas fa-video"></i> Content Creation
            </div>
            <div class="nav-tab" onclick="showSection('scheduling')">
                <i class="fas fa-calendar-alt"></i> Content Scheduling
            </div>
            <div class="nav-tab" onclick="showSection('resources')">
                <i class="fas fa-music"></i> Media Resources
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Testing Tools Section -->
            <section id="testing" class="section active">
                <h2><i class="fas fa-mobile-alt"></i> Testing & Development Tools</h2>

                <div class="card">
                    <h3><i class="fas fa-globe"></i> Web Mobile First Tool</h3>
                    <a href="https://www.webmobilefirst.com/en/" target="_blank" class="url-link">
                        <i class="fas fa-external-link-alt"></i>
                        Open Web Mobile First
                    </a>
                    <div class="instructions">
                        <h4>Usage Instructions:</h4>
                        <p>This tool helps test and optimize websites for mobile-first development. Use it to ensure
                            your web applications are responsive and mobile-friendly.</p>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-chrome"></i> Mobile Simulator Chrome Extension</h3>
                    <a href="https://chromewebstore.google.com/detail/mobile-simulator-responsi/ckejmhbmlajgoklhgbapkiccekfoccmk"
                        target="_blank" class="url-link">
                        <i class="fas fa-download"></i>
                        Install Chrome Extension
                    </a>
                    <div class="instructions">
                        <h4>Configuration:</h4>
                        <p>After installation, configure the extension to enable dark theme in settings for better
                            development experience.</p>
                    </div>
                    <div class="badge">
                        <i class="fas fa-moon"></i>
                        Dark Theme Available
                    </div>
                </div>
            </section>

            <!-- Content Creation Section -->
            <section id="content" class="section">
                <h2><i class="fas fa-video"></i> Content Creation Tools</h2>

                <div class="card">
                    <h3><i class="fas fa-closed-captioning"></i> Captions AI Desktop</h3>
                    <a href="https://desktop.captions.ai/" target="_blank" class="url-link">
                        <i class="fas fa-external-link-alt"></i>
                        Access Captions AI
                    </a>

                    <div class="warning">
                        <h4><i class="fas fa-exclamation-triangle"></i> Authentication Required</h4>
                        <p>You must first log into Atomic Mail before accessing Captions AI.</p>
                    </div>

                    <div class="instructions">
                        <h4>Setup Process:</h4>
                        <ol class="step-list">
                            <li>First, login to Atomic Mail at <a href="https://atomicmail.io/"
                                    target="_blank">atomicmail.io</a></li>
                            <li>Use the credentials provided below to access your email account</li>
                            <li>Navigate to Captions AI and enter your email address</li>
                            <li>Check Atomic Mail for the verification code</li>
                            <li>Enter the verification code to complete authentication</li>
                        </ol>
                    </div>

                    <div class="credential-item">
                        <div class="credential-label">Email Address</div>
                        <div class="credential-value">
                            <EMAIL>
                            <button class="copy-btn" onclick="copyToClipboard('<EMAIL>', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="credential-item">
                        <div class="credential-label">Password</div>
                        <div class="credential-value">
                            9m@VLePMuRjNDRk
                            <button class="copy-btn" onclick="copyToClipboard('9m@VLePMuRjNDRk', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Content Scheduling Section -->
            <section id="scheduling" class="section">
                <h2><i class="fas fa-calendar-alt"></i> Content Scheduling</h2>

                <div class="card">
                    <h3><i class="fab fa-adobe"></i> Adobe Express Scheduler</h3>
                    <a href="https://new.express.adobe.com/schedule" target="_blank" class="url-link">
                        <i class="fas fa-external-link-alt"></i>
                        Open Adobe Express
                    </a>

                    <div class="warning">
                        <h4><i class="fas fa-info-circle"></i> Scheduling Limits</h4>
                        <p>Maximum of 20 reels can be scheduled per day per Instagram account. If 20 reels are already
                            scheduled, use alternative accounts.</p>
                    </div>

                    <div class="instructions">
                        <h4>Account Management:</h4>
                        <p>Use multiple Instagram accounts to distribute content scheduling when daily limits are
                            reached.</p>
                    </div>

                    <div class="credential-item">
                        <div class="credential-label">Primary Account Email</div>
                        <div class="credential-value">
                            <EMAIL>
                            <button class="copy-btn" onclick="copyToClipboard('<EMAIL>', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="credential-item">
                        <div class="credential-label">Primary Account Password</div>
                        <div class="credential-value">
                            Little&Kid@2025
                            <button class="copy-btn" onclick="copyToClipboard('Little&Kid@2025', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="badge">
                        <i class="fas fa-users"></i>
                        Multiple Accounts Available
                    </div>
                </div>
            </section>

            <!-- Media Resources Section -->
            <section id="resources" class="section">
                <h2><i class="fas fa-music"></i> Media Resources</h2>

                <div class="card">
                    <h3><i class="fas fa-headphones"></i> Audio Assets</h3>
                    <div class="instructions">
                        <h4>Available Audio Files:</h4>
                        <p>High-quality audio files for content creation and video production.</p>
                    </div>

                    <div class="audio-player">
                        <div class="credential-label">Audio File 1</div>
                        <audio controls preload="metadata">
                            <source
                                src="https://storage.googleapis.com/captions-ai-audio-us-central1/music/0Px240QsvW0MPVSXpz5Q.mp3?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=cloud-run-captions-server%40captions-f6de9.iam.gserviceaccount.com%2F20250527%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250527T082303Z&X-Goog-Expires=86400&X-Goog-SignedHeaders=host&X-Goog-Signature=96bda55867c05c462409d0515c3aae7827d2db08b84f8e5b95a810664304cfa44900777d7c367719a059eaafaf27012173946c620cbed87b3b376b984e550dcc9c8f1a884d14115b0c48d949d3e81ebca64147b3a69964b6f475e379098ed9d2c5d1d0a2877524bc074a020490819918d22f46eaf1c458996e5e5d11cd4776a19eb2c10b3fa5752d0fa67e3f48ef769ac20c654d8cfb34d90c3d0ba1fe7e02fb0ef57e907eff0bf260a1ebc32579bd744e4d3c7ee5ccf2de76c171645f9884fefc75b28bb9824caec8f82d7a5c630d322c712815304c0600e425a0d7f003d66634eda25ded27bc86a323da808b42c18caf671b00a76a0bb3e15371fab278c4c1"
                                type="audio/mpeg">
                            Your browser does not support the audio element.
                        </audio>
                        <a href="https://storage.googleapis.com/captions-ai-audio-us-central1/music/0Px240QsvW0MPVSXpz5Q.mp3?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=cloud-run-captions-server%40captions-f6de9.iam.gserviceaccount.com%2F20250527%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250527T082303Z&X-Goog-Expires=86400&X-Goog-SignedHeaders=host&X-Goog-Signature=96bda55867c05c462409d0515c3aae7827d2db08b84f8e5b95a810664304cfa44900777d7c367719a059eaafaf27012173946c620cbed87b3b376b984e550dcc9c8f1a884d14115b0c48d949d3e81ebca64147b3a69964b6f475e379098ed9d2c5d1d0a2877524bc074a020490819918d22f46eaf1c458996e5e5d11cd4776a19eb2c10b3fa5752d0fa67e3f48ef769ac20c654d8cfb34d90c3d0ba1fe7e02fb0ef57e907eff0bf260a1ebc32579bd744e4d3c7ee5ccf2de76c171645f9884fefc75b28bb9824caec8f82d7a5c630d322c712815304c0600e425a0d7f003d66634eda25ded27bc86a323da808b42c18caf571fab278c4c1"
                            target="_blank" class="url-link" download>
                            <i class="fas fa-download"></i>
                            Download Audio File
                        </a>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(function () {
                const icon = button.querySelector('i');
                const originalClass = icon.className;

                button.classList.add('copied');
                icon.className = 'fas fa-check';

                setTimeout(() => {
                    button.classList.remove('copied');
                    icon.className = originalClass;
                }, 2000);
            }).catch(function (err) {
                console.error('Failed to copy text: ', err);
            });
        }

        // Add smooth scrolling for better UX
        document.addEventListener('DOMContentLoaded', function () {
            // Add loading animation
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease-in-out';
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>

</html>